(

  (include prefixes.clib)  

  ; asserts post-deposit treasury ring amounts, ensuring intended amount has been deposited to treasury
  ; total deposited to treasury = no. treasury coins * deposit_amount_per_coin + remainder
  (defun deposit-to-treasury (TREASURY_MOD_HASH CAT_MOD_HASH BYC_TAIL_HASH STATUTES_STRUCT
                              treasury_minimum
                              deposit_amount_per_coin ; amount being deposited into each treasury coin
                              remainder ; amount being deposited in addition to deposit_amount_per_coin into first of treasury_coins
                              first_treasury_coin
                              treasury_coins ; -> ((parent_id launcher_id ring_prev_launcher_id current_amount))
                              announcements
                              new_total_amount ; post-deposit treasury amount. must be passed 0 when function is called
                              )
    (if treasury_coins
      (assign
        (parent_id launcher_id ring_prev_launcher_id current_amount) (f treasury_coins)
        (potential_remainder next_parent_id next_launcher_id next_ring_prev_launcher_id next_current_amount) (
          if (r treasury_coins)
            (c 0x00 (f (r treasury_coins)))
            (c remainder first_treasury_coin)
        )
        delta (+ deposit_amount_per_coin potential_remainder)
        new_amount (+ next_current_amount delta)
        new_prev_amount (+ current_amount deposit_amount_per_coin (if (= launcher_id (f (r first_treasury_coin))) remainder 0))
        prev_treasury_coin_id (
          calculate-coin-id
            parent_id
            (curry_hashes CAT_MOD_HASH
              (sha256tree CAT_MOD_HASH)
              (sha256tree BYC_TAIL_HASH)
              (curry_hashes
                TREASURY_MOD_HASH
                (sha256tree TREASURY_MOD_HASH)
                (sha256tree STATUTES_STRUCT)
                (sha256tree launcher_id)
                (sha256tree ring_prev_launcher_id)
              )
            )
            current_amount
        )
        treasury_coin_id (
          calculate-coin-id
            next_parent_id
            (curry_hashes CAT_MOD_HASH
              (sha256tree CAT_MOD_HASH)
              (sha256tree BYC_TAIL_HASH)
              (curry_hashes
                TREASURY_MOD_HASH
                (sha256tree TREASURY_MOD_HASH)
                (sha256tree STATUTES_STRUCT)
                (sha256tree next_launcher_id)
                (sha256tree next_ring_prev_launcher_id)
              )
            )
            next_current_amount
        )
        (c
          ; we also send a message to each treasury coin so they can run
          (list SEND_MESSAGE 0x3f
            (concat
              PROTOCOL_PREFIX
              (sha256tree (c STATUTES_STRUCT (c delta new_amount)))
            )
            treasury_coin_id
          )
          (c
            ; check that whole ring is deposited
            (list ASSERT_COIN_ANNOUNCEMENT
              (sha256
                prev_treasury_coin_id
                (concat
                  PROTOCOL_PREFIX
                  (sha256tree
                    (c STATUTES_STRUCT
                      (c new_prev_amount
                        next_ring_prev_launcher_id ; ensure treasury coin links to prev treasury coin
                      )
                    )
                  )
                )
              )
            )
            (deposit-to-treasury
              TREASURY_MOD_HASH CAT_MOD_HASH BYC_TAIL_HASH STATUTES_STRUCT
              treasury_minimum
              deposit_amount_per_coin
              remainder
              first_treasury_coin
              (r treasury_coins)
              announcements
              (+ new_total_amount new_amount)
            )
          )
        )
      )
      (if (all (= deposit_amount_per_coin 0) (= remainder 0))
        ; this is assert balance of treasury coins, point is to prove that recharge auction can be started
        (assert (> treasury_minimum new_total_amount)
          announcements
        )
        announcements
      )
    )
  )

  (defun count-treasury-coins (treasury_coins count uniques)
    (if treasury_coins
      (count-treasury-coins
        (r treasury_coins)
        (+ 1 count)
        (if (contains uniques (f (r (f treasury_coins))))
          (x)
          (c (f (r (f treasury_coins))) uniques)
        )
      )
      count
    )
  )
)
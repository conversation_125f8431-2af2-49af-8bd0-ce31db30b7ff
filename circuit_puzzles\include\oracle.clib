(

  (include prefixes.clib)

  (defun cut-price-infos (price_info_list expiration_timestamp last_matured_price_info)
    ;; returns (last_matured_price_info (c last_matured_price_info non_matured_price_infos))
    ;; if there is a matured price_info, or (() non_matured_price_infos) otherwise, where
    ;; last_matured_price_info is the matured price_info with greatest (i.e. most recent) timestamp
    ;; and non_matured_price_infos is the list of non-matured price_infos.
    (if price_info_list
      (if (> expiration_timestamp  (r (f price_info_list)))
        (cut-price-infos (r price_info_list) expiration_timestamp (f price_info_list))
        (c last_matured_price_info
          (if last_matured_price_info
            (c last_matured_price_info price_info_list)
            price_info_list
          )
        )
      )
      (c last_matured_price_info (list last_matured_price_info))
    )
  )

  (defun filter-banned (price_announcers banned)
    (if banned
      (if price_announcers
        (if (not-contains-flat-silent banned (f (f price_announcers)))
          (c (f price_announcers) (filter-banned (r price_announcers) banned))
          (filter-banned (r price_announcers) banned)
        )
        ()
      )
      price_announcers
    )
  )

  (defun verify-announcement-asserts (input_announcement_asserts conditions)
    (if input_announcement_asserts
      (if
        (any
          (= (f (f input_announcement_asserts)) ASSERT_COIN_ANNOUNCEMENT)
          ; we only use puzzle announcements, so coin announcements are fine
          (= (f (f input_announcement_asserts)) CREATE_COIN_ANNOUNCEMENT)
        )
        (verify-announcement-asserts (r input_announcement_asserts) (c (f input_announcement_asserts) conditions))
        (x)
      )
      conditions
    )
  )

  (defun-inline abs (number)
    (if (> number MINUS_ONE) number (* number MINUS_ONE))
  )

  (defun last-item (lst)
    (if lst
      (if (r lst)
        (last-item (r lst))
        (f lst)
      )
      (x)
    )
  )

  (defun above-threshold (prices new_price auto_approve_price_threshold_bps)
    (if prices
      (if (> (/ (* (abs (- (f (f prices)) new_price)) 10000) (f (f prices))) auto_approve_price_threshold_bps)
        ONE
        (above-threshold (r prices) new_price auto_approve_price_threshold_bps)
      )
      ()
    )
  )

  (defun generate-ann-conditions (
      STATUTES_STRUCT
      ATOM_ANNOUNCER_MOD_HASH
      price_announcers
    )
    (if price_announcers
      (assign
        announcer (f price_announcers)
        ; price_announcers -> ((launcher_id args_hash price))
        (launcher_id args_hash price) announcer
        announcer_puzzle_hash (tree_hash_of_apply ATOM_ANNOUNCER_MOD_HASH args_hash)
        ; we don't need to check before_timestamp since it's already checked in atom_announcer puzzle
        (c
          (list ASSERT_PUZZLE_ANNOUNCEMENT
            (sha256
              announcer_puzzle_hash
              PROTOCOL_PREFIX
              (sha256tree
                (c STATUTES_STRUCT (c launcher_id (c ONE price))) ; ONE indicates that announcer is approved
              )
            )
          )
          (c
            (list REMARK launcher_id price)
            (generate-ann-conditions
              STATUTES_STRUCT
              ATOM_ANNOUNCER_MOD_HASH
              (r price_announcers)
            )
          )
        )
      )
      ()
    )
  )

  (defun filter (pred (@ lst (first . rest)))
    (if lst
      (assign item (a pred (list first))
        (if item
          (c item (filter pred rest))
          (filter pred rest)
        )
      )
      ()
    )
  )

  (defun list-length (lst count)
    (if lst
      (list-length (r lst) (+ count 1))
      count
    )
  )
  (defun not-contains-flat-silent (lst item)
    (if lst
      (if (= (f lst) item)
        ()
        (not-contains-flat-silent (r lst) item)
      )
      1
    )
  )

  (defun not-contains (lst item)
    (if lst
      (if (= (f (f lst)) item)
        (x)
        (not-contains (r lst) item)
      )
      1
    )
  )

  (defun unique (lst)
    (if (r lst)
      (if (not-contains (r lst) (f (f lst)))
        (unique (r lst))
        (x)
      )
      ONE
    )
  )

  (defun _reverse (L R)
    (if L
      (_reverse
        (r L)
        (c (f L) R)
      )
      R
    )
  )

  (defun-inline endp (L)
    (if L
      (if (r L)
        0
        ONE
      )
      ONE
    )
  )

  (defun _split (Z L R)
    (if (endp R)
        (c (_reverse L ()) Z)
        (_split (r Z) (c (f Z) L) (r (r R))))
    )

  (defun _merge ((@ L ((_ _ L_price) . L_rest)) (@ R ((_ _ R_price) . R_rest)) A)
    (if (any L R)
      (if L
        (if R
          (if (> R_price L_price)
            (_merge L_rest R (c (f L) A))
            (_merge L R_rest (c (f R) A))
          )
          (_merge L_rest () (c (f L) A))
        )
        (_merge () R_rest (c (f R) A))
      )
      (_reverse A ())
    )
  )

  ; thanks @geoff for the mergesort implementation on https://aggsig.me/mergesort.html
  (defun mergesort (Z)
    (if Z
      (if (r Z)
        (assign
          (L . R) (_split Z () Z)
          (_merge (mergesort L) (mergesort R) ())
        )
        Z
      )
      ()
    )
  )

  (defun-inline get-median-index (length)
    ; if odd, we return the middle value
    ; if even, we return the one smaller since prices are sorted in descending order
    (/ length TWO)
  )

  (defun get-price (prices index curr_index)
    (if prices
      (if (= index curr_index)
        (f prices)
        (get-price (r prices) index (+ curr_index ONE))
      )
      (x)
    )
  )
)
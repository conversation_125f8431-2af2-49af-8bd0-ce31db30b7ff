(mod (
    CAT_MOD_HASH TREASURY_MOD_HASH
    (MOD_HASH STATUTES_STRUCT BYC_TAIL_HASH LAUNCHER_ID AUCTION_PARAMS LAST_BID)
    lineage_proof input_conditions
    (
      statutes_inner_puzzle_hash
      current_time
      auction_ttl
      bid_ttl
      min_crt_price
      min_byc_bid_amount
      min_bid_increase_bps
      treasury_minimum
      treasury_coins ; -> ((parent_id launcher_id ring_prev_launcher_id current_amount))
    )
  )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include statutes_utils.clib)
  (include utils.clib)
  (include recharge_auction.clib)

  (assign
    statutes_puzzle_hash (calculate-statutes-puzzle-hash STATUTES_STRUCT statutes_inner_puzzle_hash)
    auction_params (list
      current_time
      auction_ttl
      bid_ttl
      min_crt_price
      min_byc_bid_amount
      min_bid_increase_bps
    )
    treasury_coins_count (count-treasury-coins treasury_coins 0 ())
    (assert
      (> treasury_coins_count 0)
      LAUNCHER_ID
      ; can't run if LAST_BID is set. must settle first
      (= LAST_BID ())
      ; can restart if auction timed out before receiving a bid
      (if AUCTION_PARAMS
        (> (- current_time (f AUCTION_PARAMS)) (f (r AUCTION_PARAMS)))
        1
      )
      (li
        (list
          CREATE_COIN
          (curry_hashes
            MOD_HASH
            (sha256 ONE MOD_HASH)
            (sha256tree STATUTES_STRUCT)
            (sha256 ONE LAUNCHER_ID)
            (sha256tree auction_params)
            (sha256 ONE ())
          )
          0
        )
        (list REMARK
          PROTOCOL_PREFIX
          LAUNCHER_ID
          auction_params
          () ; no bids
        )
        (assert-statute statutes_puzzle_hash STATUTE_RECHARGE_AUCTION_TTL auction_ttl)
        (assert-statute statutes_puzzle_hash STATUTE_RECHARGE_AUCTION_BID_TTL bid_ttl)
        (assert-statute statutes_puzzle_hash STATUTE_RECHARGE_AUCTION_MINIMUM_CRT_PRICE min_crt_price)
        (assert-statute statutes_puzzle_hash STATUTE_RECHARGE_AUCTION_MINIMUM_BID min_byc_bid_amount)
        (assert-statute statutes_puzzle_hash STATUTE_MINIMUM_BID_INCREASE_BPS min_bid_increase_bps)
        (assert-statute statutes_puzzle_hash STATUTE_TREASURY_MINIMUM treasury_minimum)
        ; start_time minus one tx block time should already be in the past
        (list ASSERT_SECONDS_ABSOLUTE (- current_time MAX_TX_BLOCK_TIME))
        ; make sure that start_time hasn't happened yet, allow it to be in mempool for 5 tx blocks
        (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_time (* 5 MAX_TX_BLOCK_TIME)))
        ; start balance should be zero
        (list ASSERT_MY_AMOUNT 0)
        (list ASSERT_MY_PUZZLE_HASH
          (curry_hashes CAT_MOD_HASH
            (sha256 ONE CAT_MOD_HASH)
            (sha256 ONE BYC_TAIL_HASH)
            (curry_hashes
              MOD_HASH
              (sha256 ONE MOD_HASH)
              (sha256tree STATUTES_STRUCT)
              (sha256 ONE LAUNCHER_ID)
              (sha256tree AUCTION_PARAMS)
              (sha256 ONE LAST_BID)
            )
          )
        )
        (list ASSERT_MY_PARENT_ID
          (calculate-coin-id
            (f lineage_proof)
            (curry_hashes
              CAT_MOD_HASH
              (sha256 ONE CAT_MOD_HASH)
              (sha256 ONE BYC_TAIL_HASH)
              (tree_hash_of_apply MOD_HASH (f (r (r lineage_proof))))
            )
            ; parent bid amount
            (f (r lineage_proof))
          )

        )
        &rest
        ; deposit to treasury = no. treasury coins * deposit per coin + remainder
        (deposit-to-treasury
          TREASURY_MOD_HASH
          CAT_MOD_HASH
          BYC_TAIL_HASH
          STATUTES_STRUCT
          treasury_minimum
          0 ; deposit per coin
          0 ; deposit remainder
          (f treasury_coins)
          treasury_coins
          input_conditions
          0 ; arg to sum up post-deposit treasury amounts
        )
      )
    )
  )
)
;; Bid to active surplus auction coin. There can be multiple active surplus auctions.
;; Surplus auction puzzle is inner puzzle of CRT coin. So bids are are in CRT CAT values.
;; The bid amount is bidding for BYC_LOT_AMOUNT. Bid amount must be at least STATUTE_MINIMUM_BID_INCREASE_BPS higher
;; than the current bid if any to avoid spamming the network with small bids.
(mod
  (OFFER_MOD_HASH
    (MOD_HASH CAT_MOD_HASH STATUTES_STRUCT BYC_TAIL_HASH CRT_TAIL_HASH LAUNCHER_ID BID_TTL MIN_BID_INCREASE_BPS BYC_LOT_AMOUNT LAST_BID)
    lineage_proof ; -> (parent_parent_id parent_amount parent_cat_inner_puzzle_hash)
    input_conditions
    (
      bid_crt_amount
      target_puzzle_hash
      current_timestamp
      my_amount
      my_coin_id
    )
  )

  (include *standard-cl-23.1*)
  (include prefixes.clib)
  (include curry.clib)
  (include condition_codes.clib)
  (include utils.clib)

  (assign
    (last_target_puzzle_hash . last_bid_timestamp) (if LAST_BID LAST_BID (c 0 0))
    (assert
      ; we need lineage proof to ensure that a CAT lot is associated with this surplus auction
      (l lineage_proof)
      ; must be higher than current bid (keepers are bidding for smallest amount of CRT per BYC lot)
      (any (all (= my_amount 0) (> bid_crt_amount 0)) (> bid_crt_amount my_amount))
      ; TTL for bid hasn't expired yet
      (any (not last_bid_timestamp) (> BID_TTL (- current_timestamp last_bid_timestamp)))
      ; bid_crt_amount is min_bid_increase_bps higher than my_amount
      (> bid_crt_amount (/ (* my_amount (+ PRECISION_BPS MIN_BID_INCREASE_BPS)) PRECISION_BPS))
      (li
        (list ASSERT_MY_AMOUNT my_amount)
        (list ASSERT_MY_PUZZLE_HASH
          (curry_hashes CAT_MOD_HASH
            (sha256 ONE CAT_MOD_HASH)
            (sha256 ONE CRT_TAIL_HASH)
            (curry_hashes
              MOD_HASH
              (sha256 ONE MOD_HASH)
              (sha256tree STATUTES_STRUCT)
              (sha256 ONE LAUNCHER_ID)
              (sha256 ONE BID_TTL)
              (sha256 ONE MIN_BID_INCREASE_BPS)
              (sha256 ONE BYC_LOT_AMOUNT)
              (sha256tree LAST_BID)
            )
          )
        )
        ; recreate with new top bid and new timestamp
        (list
          CREATE_COIN
          (curry_hashes
            MOD_HASH
            (sha256 ONE MOD_HASH)
            (sha256tree STATUTES_STRUCT)
            (sha256 ONE LAUNCHER_ID)
            (sha256 ONE BID_TTL)
            (sha256 ONE MIN_BID_INCREASE_BPS)
            (sha256 ONE BYC_LOT_AMOUNT)
            (sha256tree (c target_puzzle_hash current_timestamp))
          )
          bid_crt_amount
        )
        (list REMARK  ; this is for the driver to create the reveal
          PROTOCOL_PREFIX
          LAUNCHER_ID
          BID_TTL
          MIN_BID_INCREASE_BPS
          BYC_LOT_AMOUNT
          bid_crt_amount
          (c target_puzzle_hash current_timestamp)
        )
        ; current_time minus one tx block time should already be in the past
        (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
        ; make sure that current_timestamp hasn't happen yet, allow it to be in mempool for 5 tx blocks
        (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp (* 5 MAX_TX_BLOCK_TIME)))
        (list ASSERT_MY_COIN_ID my_coin_id)
        ; ensure lineage so we don't lose track of the lot
        (list ASSERT_MY_PARENT_ID
          (sha256
            (f lineage_proof)
            (curry_hashes
              CAT_MOD_HASH
              (sha256 ONE CAT_MOD_HASH)
              (sha256 ONE CRT_TAIL_HASH)
              (tree_hash_of_apply MOD_HASH (f (r (r lineage_proof))))
            )
            ; parent bid amount
            (f (r lineage_proof))
          )
        )
        &rest
        (if last_target_puzzle_hash
          (c
            ; assert that keeper absorbed their collateral + left over mojos from melting
            ; this also confirms that winning bid owner received the lot
            (list ASSERT_PUZZLE_ANNOUNCEMENT
              (sha256
                (curry_hashes CAT_MOD_HASH
                  (sha256 ONE CAT_MOD_HASH)
                  (sha256 ONE CRT_TAIL_HASH)
                  OFFER_MOD_HASH
                )
                (sha256tree
                  (c my_coin_id
                    (list
                      (list last_target_puzzle_hash my_amount (list last_target_puzzle_hash))
                    )
                  )
                )
              )
            )
            input_conditions
          )
          input_conditions
        )
      )
    )
  )
)
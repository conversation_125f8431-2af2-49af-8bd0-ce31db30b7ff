; governance
; we either activate this announcer or deactivate, can happen at any time via CRT vote
(mod ((MOD_HASH STATUTES_STRUCT LAUNCHER_ID INNER_PUZZLE_HASH APPROVED DEPOSIT DELAY VALUE MIN_DEPOSIT
        CLAIM_COUNTER COOLDOWN_START PENALIZABLE_AT TIMESTAMP_EXPIRES
      )
      ; solution
      inner_puzzle_hash new_puzzle_hash deposit input_conditions
      (
        statutes_inner_puzzle_hash
        input_toggle_activation
        max_disapproval_penalty_factor_or_min_deposit ; penalty factor when disapproving, min deposit when approving
      )
    )

  (include *standard-cl-23.1*)
  (include condition_codes.clib)
  (include curry.clib)
  (include utils.clib)
  (include statutes_utils.clib)
  (include announcer.clib)

  (assign
    toggle_activation (assert
      (any
        (all (= input_toggle_activation 0) APPROVED)
        (all (= input_toggle_activation 1) (not APPROVED))
      )
      input_toggle_activation
    )
    statutes_puzzle_hash (calculate-statutes-puzzle-hash STATUTES_STRUCT statutes_inner_puzzle_hash)
    operation_conditions (recreate-myself-condition MOD_HASH
      STATUTES_STRUCT
      LAUNCHER_ID
      INNER_PUZZLE_HASH
      toggle_activation
      deposit
      DELAY
      VALUE
      MIN_DEPOSIT
      CLAIM_COUNTER
      COOLDOWN_START
      PENALIZABLE_AT
      TIMESTAMP_EXPIRES
      (if (= toggle_activation 0)
        ; we're disapproving. use deposit for fees
        (assign
          min_penalized_deposit (- DEPOSIT (/ (/ (* DEPOSIT PRECISION max_disapproval_penalty_factor_or_min_deposit) PRECISION) PRECISION_BPS))
          (assert
            ; can't deduct more for fees than penalty
            (all (> (+ DEPOSIT 1) deposit) (> deposit (- min_penalized_deposit 1)))
            (c
              (list RESERVE_FEE (- DEPOSIT deposit))
              (c
                (assert-statute statutes_puzzle_hash STATUTE_ANNOUNCER_DISAPPROVAL_MAXIMUM_PENALTY_BPS max_disapproval_penalty_factor_or_min_deposit)
                input_conditions
              )
            )
          )
        )
        ; we're approving
        (assert
          ; we're not requiring that DELAY <= Statutes max delay or that price is not expired in case an announcer has a
          ;  technical issue just when governance wants to enact approval. but we always want DEPOSIT and MIN_DEPOSIT
          ;  to be set properly so that announcer can be penalized
          (= deposit DEPOSIT) ; can't steal deposit
          ; deposit must be no less than both announcer and statutes min deposit
          (> deposit (- MIN_DEPOSIT 1))
          (> MIN_DEPOSIT (- max_disapproval_penalty_factor_or_min_deposit 1))
          (c
            (assert-statute statutes_puzzle_hash STATUTE_ANNOUNCER_MINIMUM_DEPOSIT max_disapproval_penalty_factor_or_min_deposit)
            input_conditions
          )
        )
      )
    )
    (li
      (list ASSERT_PUZZLE_ANNOUNCEMENT
        (sha256
          statutes_puzzle_hash
          PROTOCOL_PREFIX
          CUSTOM_CONDITION_PREFIX
          (sha256tree (c LAUNCHER_ID toggle_activation))
        )
      )
      &rest
      operation_conditions
    )
  )
)
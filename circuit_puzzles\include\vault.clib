(

  (defun-inline undiscount-principal (discounted_principal cumulative_stability_fee_df)
    (* MINUS_ONE (/ (* MINUS_ONE discounted_principal cumulative_stability_fee_df) PRECISION))
  )

  (defun-inline available-to-mint (collateral debt liquidation_ratio_pct xch_price)
    ; returns amount of byc that can be borrowed + 1
    ; used when borrowing
    (- (/ (/ (* collateral xch_price 1000 PRECISION_PCT) (* liquidation_ratio_pct PRICE_PRECISION)) MOJOS) debt)
  )

  (defun-inline get-min-collateral-amount (debt liquidation_ratio_pct xch_price)
    ; returns min collateral value for given debt, liquidation ratio and XCH price. Multiplications by -1 to ceil the division
    ; used when withdrawing collateral and starting liquidation auctions
    (* MINUS_ONE (/ (* MINUS_ONE debt PRICE_PRECISION liquidation_ratio_pct MOJOS) (* xch_price PRECISION_PCT 1000)))
  )

  (defun-inline calculate-total-fees (undiscounted_principal principal liquidation_penalty_bps)
    (assign
      stability_fees (if (> undiscounted_principal principal)
        (- undiscounted_principal principal)
        0
      )
      debt (+ principal stability_fees)
      liquidation_penalty (* MINUS_ONE (/ (* MINUS_ONE debt liquidation_penalty_bps) PRECISION_BPS))
      (+ stability_fees liquidation_penalty) ; total fees
    )
  )

  (defun-inline calculate-byc-coin-id (CAT_MOD_HASH BYC_TAIL_HASH (parent_id amount inner_puzzle_hash))
    (calculate-coin-id
      parent_id
      (curry_hashes CAT_MOD_HASH
        (sha256 ONE CAT_MOD_HASH)
        (sha256 ONE BYC_TAIL_HASH)
        ; inner puzzle for cat coin
        inner_puzzle_hash
      )
      amount
    )
  )

)
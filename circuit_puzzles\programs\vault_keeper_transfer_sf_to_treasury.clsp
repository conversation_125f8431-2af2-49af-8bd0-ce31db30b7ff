(mod
  (
    CAT_MOD_HASH BYC_TAIL_MOD_HASH RUN_TAIL_MOD_HASH TREASURY_MOD_HASH
    (@ VAULT_STATE
      (
       COLLATERAL
       PRINCIPAL AUCTION_STATE INNER_PUZZLE_HASH
       STATUTES_STRUCT DISCOUNTED_PRINCIPAL statutes_puzzle_hash
      )
    )
    (@ args
      (
        byc_issuing_coin_info  ; -> (parent_id amount inner_puzzle_hash)
        statutes_cumulative_stability_fee_df
        treasury_coin_info
        min_treasury_delta
        current_timestamp
        current_stability_fee_df
        price_info
      )
    )
  )

  (include *standard-cl-23.1*)
  (include utils.clib)
  (include vault.clib)
  (include statutes_utils.clib)
  (include condition_codes.clib)
  (include curry.clib)

  (assign
    byc_tail_hash (curry_hashes BYC_TAIL_MOD_HASH (sha256tree STATUTES_STRUCT))
    byc_principal_to_melt PRINCIPAL
    cumulative_stability_fee_df (calculate-cumulative-discount-factor
      statutes_cumulative_stability_fee_df
      current_stability_fee_df
      current_timestamp
      (r price_info)
    )
    undiscounted_principal (undiscount-principal DISCOUNTED_PRINCIPAL cumulative_stability_fee_df)
    fees_to_treasury (calculate-total-fees undiscounted_principal PRINCIPAL 0)
    (treasury_parent treasury_launcher_id treasury_prev_launcher_id treasury_amount) treasury_coin_info
    new_treasury_amount (+ fees_to_treasury treasury_amount)
    byc_issuing_coin_id (calculate-byc-coin-id
      CAT_MOD_HASH
      byc_tail_hash
      (list
        (f byc_issuing_coin_info) ; parent_id
        (r byc_issuing_coin_info) ; amount
        RUN_TAIL_MOD_HASH
      )
    )
    treasury_coin_id (calculate-byc-coin-id
      CAT_MOD_HASH
      byc_tail_hash
      (list
        treasury_parent
        treasury_amount
        (curry_hashes
          TREASURY_MOD_HASH
          (sha256 ONE TREASURY_MOD_HASH)
          (sha256tree STATUTES_STRUCT)
          (sha256 ONE treasury_launcher_id)
          (sha256 ONE treasury_prev_launcher_id)
        )
      )
    )
    (assert
      ; only if there are fees to transfer
      (> fees_to_treasury 0)
      ; to avoid keepers dusting and hogging treasury coins
      (> fees_to_treasury min_treasury_delta)
      ; only if vault is not in liquidation or bad debt
      (not AUCTION_STATE)
      ; issuing coin must have the same as amount we're transferring
      (= (r byc_issuing_coin_info) fees_to_treasury)
      (list
        (list
          COLLATERAL
          (+ PRINCIPAL fees_to_treasury)
          AUCTION_STATE
          INNER_PUZZLE_HASH
          DISCOUNTED_PRINCIPAL
        )
        (list
          ; signal to tail that it can issue BYC with certain amount
          (list SEND_MESSAGE 0x3f
            (concat PROTOCOL_PREFIX (sha256tree (c STATUTES_STRUCT (c "i" fees_to_treasury))))
            byc_issuing_coin_id
          )
          ; approve treasury coin to receive a deposit
          (list SEND_MESSAGE 0x3f
            (concat
              PROTOCOL_PREFIX
              (sha256tree (c STATUTES_STRUCT (c fees_to_treasury new_treasury_amount)))
            )
            treasury_coin_id
          )
          (assert-statute statutes_puzzle_hash STATUTE_CUMULATIVE_STABILITY_FEE_DF statutes_cumulative_stability_fee_df)
          (assert-statute statutes_puzzle_hash STATUTE_TREASURY_MINIMUM_DELTA min_treasury_delta)
          (assert-price-info statutes_puzzle_hash price_info)
          (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
          (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp MAX_TX_BLOCK_TIME))
          (assert-statute statutes_puzzle_hash STATUTE_STABILITY_FEE_DF current_stability_fee_df)
        )
      )
    )
  )
)

# The last section of this file is generated.  Please locate custom edits
# outside of that area and only update that section using the original site
# that generated it.  Links provided below.

tmp/

# C extensions
**/*.o
**/*.DS_Store

# (Spac)Emacs
**/*~
**/#*#
**/.#*

# Database
nohup.out
mongod.log*
fndb_test*
blockchain_test*
*.db
*.db-journal

# Logs
*.log
*.out

# Keys and plot files
config/keys.yaml
config/plots.yaml

# Bundled code
chia-blockchain.tar.gz.tar.gz

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
build_scripts/build

# Installer logs
**/*.egg-info

# PoSpace plots
**/*.dat
**/*.dat.tmp
*.mo
*.pot

# pyenv
.python-version
venv*/
activate

# Editors
.vscode
.idea

# Packaging
chia-blockchain.tar.gz

# Timelord utilities
vdf_bench

# Node modules
**/node_modules

# Offer Files
*.offer

# Backup Files
*.backup

# Attest Files
*.attest

# Compiled CLVM
main.sym
*.recompiled

# Profiling
*.prof
*.pstats

# Dev config react
# chia-blockchain-gui/src/dev_config.js
# React built app
chia-blockchain-gui/.eslintcache
chia-blockchain-gui/build
build_scripts/dist
build_scripts/*.Dmg
chia-blockchain-gui/src/locales/_build
build_scripts\win_build
build_scripts/win_build
win_code_sign_cert.p12

# chia-blockchain wheel build folder
build/

# Temporal `n` (node version manager) directory
.n/

# pytest-monitor
# https://pytest-monitor.readthedocs.io/en/latest/operating.html?highlight=.pymon#storage
.pymon

# cache for tooling such as tools/manage_clvm.py
.chia_cache/

#       =====                       =====
#     DO NOT EDIT BELOW - GENERATED
#       =====                       =====
#
# If you want to modify below please use the site linked below to generate an update

# Created by https://www.toptal.com/developers/gitignore/api/python,git,vim
# Edit at https://www.toptal.com/developers/gitignore?templates=python,git,vim

### Git ###
# Created by git for backups. To disable backups in Git:
# $ git config --global mergetool.keepBackup false
*.orig

# Created by git when using merge tools for conflicts
*.BACKUP.*
*.BASE.*
*.LOCAL.*
*.REMOTE.*
*_BACKUP_*.txt
*_BASE_*.txt
*_LOCAL_*.txt
*_REMOTE_*.txt

### Python ###
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
mypy.ini

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintainted in a separate JetBrains.gitignore that can
#  be found at https://github.com/github/gitignore/blob/main/Global/JetBrains.gitignore
#  and can be added to the global gitignore or merged into this file.  For a more nuclear
#  option (not recommended) you can uncomment the following to ignore the entire idea folder.
#.idea/

### Vim ###
# Swap
[._]*.s[a-v][a-z]
!*.svg  # comment out if you don't need vector files
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# Session
Session.vim
Sessionx.vim

# Temporary
.netrwhist
*~
# Auto-generated tag files
tags
# Persistent undo
[._]*.un~

# End of https://www.toptal.com/developers/gitignore/api/python,git,vim

# Ignore the binaries that are pulled for the installer
/bladebit/
/madmax/
*.sym
(mod (GOVERNANCE_MOD_HASH CAT_MOD_HASH CRT_TAIL_HASH STATUTES_STRUCT_HASH current_timestamp inner_puzzle_hash my_parents_parent_id amount)
  (include condition_codes.clib)
  (include curry.clib)

  (defconstant ONE 1)
  (defun sha256tree (TREE)
    (if (l TREE)
      (sha256 TWO (sha256tree (f TREE)) (sha256tree (r TREE)))
      (sha256 ONE TREE)
    )
  )

  (list
    (list CREATE_COIN
      (curry_hashes GOVERNANCE_MOD_HASH
        (sha256 ONE GOVERNANCE_MOD_HASH)
        (sha256 ONE CRT_TAIL_HASH)
        STATUTES_STRUCT_HASH
        (sha256 ONE inner_puzzle_hash)
        (sha256 ONE ())
      )
      amount
      (list inner_puzzle_hash)
    )
    (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp MAX_TX_BLOCK_TIME ))
    ; current_timestamp - 1 min should've passed already too, this is to ensure current timestamp is
    ; within a boundary of last_block > current_timestamp - 1 block < next block
    (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
    (list ASSERT_MY_PARENT_ID
      (coinid
        my_parents_parent_id
        (curry_hashes CAT_MOD_HASH
          (sha256 ONE CAT_MOD_HASH)
          (sha256 ONE CRT_TAIL_HASH)
          inner_puzzle_hash
        )
        amount
      )
    )
    (list ASSERT_MY_AMOUNT amount)
    ; confirm that newly created coin was spent
    (list RECEIVE_MESSAGE
      0x3f
      '*'
      (coinid
        my_parents_parent_id
        (curry_hashes CAT_MOD_HASH
          (sha256 ONE CAT_MOD_HASH)
          (sha256 ONE CRT_TAIL_HASH)
          inner_puzzle_hash
        )
        amount
      )
    )
    (list REMARK 'C' () () inner_puzzle_hash)
  )
)
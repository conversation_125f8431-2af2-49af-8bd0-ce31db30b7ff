(mod
  (
    (BILL MOD_HASH INNER_PUZZLE_HASH CAT_MOD_HASH CRT_TAIL_HASH STATUTES_STRUCT
     statutes_puzzle_hash)
    (
      amount
      parent_veto_id
      veto_amount
      veto_inner_puzzle_hash
      veto_bill_hash
      current_timestamp
    )
  )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include prefixes.clib)
  (include utils.clib)

  (defconst ANN_VETOED 'x')

  (assign
    (
      (veto_period_expiry implemenation_delay_expiry . implementation_interval_expiry)
      new_statute_index
      new_statute_value
      new_threshold_amount_to_propose
      new_veto_seconds
      new_delay_seconds
      new_max_delta
    ) BILL
    (assert
      ; can only veto if in proposal mode (BILL is not empty)
      BILL
      (> veto_amount amount)
      (> veto_period_expiry current_timestamp)
      (list
        () ; set BILL to empty on successful veto
        (list
          (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
          (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp (* 5 MAX_TX_BLOCK_TIME)))
          (list CREATE_COIN_ANNOUNCEMENT (concat PROTOCOL_PREFIX (sha256tree ANN_VETOED)))
          ; assert announcement from another CRT coin with higher amount than this one
          (list RECEIVE_MESSAGE 0x3f
            (concat PROTOCOL_PREFIX (sha256tree BILL))
            (calculate-coin-id
              parent_veto_id
              ; governance coin puzzle hash
              (curry_hashes CAT_MOD_HASH
                (sha256 ONE CAT_MOD_HASH)
                (sha256 ONE CRT_TAIL_HASH)
                (curry_hashes MOD_HASH
                  (sha256 ONE MOD_HASH)
                  (sha256 ONE CRT_TAIL_HASH)
                  (sha256tree STATUTES_STRUCT)
                  (sha256 ONE veto_inner_puzzle_hash)
                  veto_bill_hash
                )
              )
              veto_amount
            )
          )
        )
      )
    )
  )
)
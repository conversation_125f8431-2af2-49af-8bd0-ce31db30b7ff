; Circuit CAT token (CRT) TAIL program
; CRT token is used for governance voting and can be used to bid in surplus auctions.
; CRT tokens are minted at protocol launch, by announcer registry and recharge auctions.
; CRT tokens are melted in surplus auctions.
(mod (RUN_TAIL_MOD_HASH
      STATUTES_STRUCT
      Truths
      parent_is_cat
      lineage_proof
      delta
      inner_conditions
      solution
     )

  (include *standard-cl-23.1*)
  (include condition_codes.clib)
  (include cat_truths.clib)
  (include curry.clib)
  (include utils.clib)
  (include statutes_utils.clib)
  (include condition_filtering.clib)

  (defun-inline get-statutes-puzzle-hash (statutes_struct statutes_inner_puzzle_hash)
    (c
      (sha256tree statutes_struct)
      (calculate-statutes-puzzle-hash statutes_struct statutes_inner_puzzle_hash)
    )
  )

  (defun-inline abs (number)
    (if (> number MINUS_ONE) number (* number MINUS_ONE))
  )

  (if (all
        solution
        (> 0 delta)
        (fail-on-protocol-condition inner_conditions)
      )
    (assign  ; issuing by recharge auction, announcer registry or melting by surplus
      CAT_MOD_HASH (cat_mod_hash_truth Truths)
      CAT_MOD_HASH_HASH (cat_mod_hash_hash_truth Truths)
      crt_tail_hash (cat_tail_program_hash_truth Truths)
      (
        approval_mod_hash
        approver_parent_id
        approver_curried_mod_args
        byc_tail_hash
        approver_amount
        delta_amount
        approval_mod_hashes
        statutes_inner_puzzle_hash
        target_puzzle_hash  ; where this coin is going to, if issuance
      ) solution
      (statutes_struct_hash . statutes_puzzle_hash) (get-statutes-puzzle-hash STATUTES_STRUCT statutes_inner_puzzle_hash)
      approver_coin_id (calculate-coin-id
        approver_parent_id
        ; approval_mod_hashes is defined by statutes and we validate it by checking mod_hash is at the correct position
        (if byc_tail_hash
          (assert
            (= approval_mod_hash (f (r (r approval_mod_hashes))))
            ; because we don't provide create coins as input conditions
            ; delta will always be in melting mode but delta_amount will be positive
            (= 0 (+ delta_amount delta))
            ; a CAT puzzle with recharge auction as inner puzzle
            (curry_hashes CAT_MOD_HASH
              CAT_MOD_HASH_HASH
              (sha256 ONE byc_tail_hash)
              (tree_hash_of_apply approval_mod_hash approver_curried_mod_args)
            )
          )
          (if (= approval_mod_hash (f (r approval_mod_hashes)))
            ; OR surplus puzzle for melting
            (assert
              ; we're now actually melting, delta_amount will be negative
              (= 0 (- delta_amount delta))
              parent_is_cat  ; must be a cat
              (curry_hashes CAT_MOD_HASH
                CAT_MOD_HASH_HASH
                (sha256 ONE crt_tail_hash)
                (tree_hash_of_apply approval_mod_hash approver_curried_mod_args)
              )
            )
            ; OR announcer registry
            (assert
              (= approval_mod_hash (f (r (r (r (r approval_mod_hashes)))))) ; announcer registry mod hash position in approved hashes
              ; because we don't provide create coins as input conditions
              ; delta will always be in melting mode but delta_amount will be positive
              (= 0 (+ delta_amount delta))
              (tree_hash_of_apply approval_mod_hash approver_curried_mod_args)
            )
          )
        )
        approver_amount
      )
      ; only if we're minting
      curried_target_puzzle_hash (if target_puzzle_hash
        (curry_hashes CAT_MOD_HASH
          CAT_MOD_HASH_HASH
          (sha256 ONE crt_tail_hash)
          target_puzzle_hash
        )
        ()
      )
      (list
        (list CREATE_COIN_ANNOUNCEMENT "$") ; for protocol external coins to assert issuance
        (if curried_target_puzzle_hash
          (list CREATE_COIN curried_target_puzzle_hash delta_amount (list target_puzzle_hash))
          (list REMARK)
        )
        (list RECEIVE_MESSAGE 0x3f
          (concat
            PROTOCOL_PREFIX
            (sha256tree
              (c STATUTES_STRUCT ; this binds us to the same protocol or statutes struct singleton
                (c target_puzzle_hash delta_amount) ; get approval for target puzzle hash and amount from approver
              )
            )
          )
          approver_coin_id
        )
        (list ASSERT_MY_PUZZLE_HASH
          (curry_hashes CAT_MOD_HASH
            CAT_MOD_HASH_HASH
            (sha256tree crt_tail_hash)
            RUN_TAIL_MOD_HASH
          )
        )
        (list ASSERT_MY_AMOUNT (abs delta_amount))
        (assert-statute statutes_puzzle_hash STATUTE_APPROVAL_MOD_HASHES_HASH (sha256tree approval_mod_hashes))
      )
    )
    (assert (= delta 0)
      (list ; issuance at launch of protocol
        ; receive approval from statutes launcher
        (list ASSERT_CONCURRENT_SPEND (f (r STATUTES_STRUCT)))
      )
    )
  )
)

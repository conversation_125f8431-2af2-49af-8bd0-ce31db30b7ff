(mod
  (
    (@ VAULT_STATE
      (
       COLLATERAL PRINCIPAL AUCTION_STATE INNER_PUZZLE
       STATUTES_STRUCT DISCOUNTED_PRINCIPAL
       statutes_puzzle_hash
      )
    )
    (@ args
      (
        withdraw_amount
        price_info
        liquidation_ratio
        current_timestamp
        statutes_cumulative_stability_fee_df
        current_stability_fee_df
      )
    )
  )

  (include *standard-cl-23.1*)
  (include utils.clib)
  (include vault.clib)
  (include statutes_utils.clib)
  (include condition_codes.clib)

  (assign
    collateral (- COLLATERAL withdraw_amount)
    cumulative_stability_fee_df (calculate-cumulative-discount-factor
      statutes_cumulative_stability_fee_df
      current_stability_fee_df
      ; don't allow anyone to exploit some time to get tx into mempool and borrow from the future
      (+ current_timestamp (* 3 MAX_TX_BLOCK_TIME))
      (r price_info)
    )
    undiscounted_principal (undiscount-principal DISCOUNTED_PRINCIPAL cumulative_stability_fee_df)
    min_collateral_amount (
      get-min-collateral-amount
        undiscounted_principal
        liquidation_ratio
        (f price_info)
    )
    (assert
      (> collateral -1)
      (> COLLATERAL collateral)
      (> collateral (- min_collateral_amount 1))
      (list
        (list collateral PRINCIPAL AUCTION_STATE INNER_PUZZLE DISCOUNTED_PRINCIPAL)
        (list
          ; validate current timestamp is within bounds
          (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
          (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp MAX_TX_BLOCK_TIME))
          ; assert liquidation ratio
          (assert-statute statutes_puzzle_hash STATUTE_VAULT_LIQUIDATION_RATIO_PCT liquidation_ratio)
          ; assert collateral price
          (assert-price-info statutes_puzzle_hash price_info)
          (assert-statute statutes_puzzle_hash STATUTE_STABILITY_FEE_DF current_stability_fee_df)
          ; assert cumulative stability fee rate
          (assert-statute statutes_puzzle_hash STATUTE_CUMULATIVE_STABILITY_FEE_DF statutes_cumulative_stability_fee_df)
        )
      )
    )
  )
)

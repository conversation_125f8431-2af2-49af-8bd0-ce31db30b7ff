(
  
  (include prefixes.clib)

  (defun-inline recreate-myself-condition (MOD_HASH STATUTES_STRUCT LAUNCHER_ID inner_puzzle_hash approved
                                           deposit delay value min_deposit
                                           claim_counter ; NEW
                                           cooldown_start penalizable_at
                                           timestamp_expires input_conditions)
    (c
      (list CREATE_COIN
        (curry_hashes MOD_HASH
          (sha256 ONE MOD_HASH)
          (sha256tree STATUTES_STRUCT)
          (sha256 ONE LAUNCHER_ID)
          (sha256 ONE inner_puzzle_hash)
          (sha256 ONE approved)
          (sha256 ONE deposit)
          (sha256 ONE delay)
          (sha256tree value)
          (sha256 ONE min_deposit)
          (sha256 ONE claim_counter) ; NEW
          (sha256 ONE cooldown_start)
          (sha256 ONE penalizable_at)
          (sha256 ONE timestamp_expires)
        )
        deposit
        (list inner_puzzle_hash)
      )
      ; driver remark to reveal the puzzle hash
      (c
        (list REMARK PROTOCOL_PREFIX LAUNCHER_ID inner_puzzle_hash deposit approved delay value min_deposit
          claim_counter ; NEW
          cooldown_start penalizable_at timestamp_expires
        )
        input_conditions
      )
    )
  )

)
(mod
  (
    (@ VAULT_STATE
      (
       COLLATERAL PRINCIPAL AUCTION_STATE INNER_PUZZLE_HASH
       STATUTES_STRUCT DISCOUNTED_PRINCIPAL
       statutes_puzzle_hash
      )
    )
    (@ args
      (
        deposit_amount
      )
    )
  )
  (if (> deposit_amount 0)
    (list
      (list
        (+ deposit_amount COLLATERAL)
        PRINCIPAL
        AUCTION_STATE
        INNER_PUZZLE_HASH
        DISCOUNTED_PRINCIPAL)
      ; nothing else to assert here, just create a coin with new amount
      (list )
    )
    (x)
  )
)
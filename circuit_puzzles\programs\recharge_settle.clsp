; This program is used to settle the winning recharge auction bid.
; Settlement is possible once the auction has ended, i.e. when the last bid has not been outbid after bid_ttl seconds.
; Running this operation deposits the BYC locked into this coin (recharge auction) into the treasury
; and issues CRT to the target puzzle hash of the winning bid.
(mod (
    CAT_MOD_HASH
    RUN_TAIL_MOD_HASH
    TREASURY_MOD_HASH
    (MOD_HASH STATUTES_STRUCT BYC_TAIL_HASH LAUNCHER_ID AUCTION_PARAMS LAST_BID)
    lineage_proof input_conditions
    (
      current_timestamp
      treasury_coins
      funding_coin_info
      target_puzzle_hash
      crt_tail_hash
    )
  )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include utils.clib)
  (include recharge_auction.clib)

  (assign-lambda
    bid_ttl (f (r (r AUCTION_PARAMS)))
    ((winning_byc_bid_amount . winning_crt_bid_amount) target_puzzle_hash timestamp) LAST_BID
    treasury_coins_count (count-treasury-coins treasury_coins 0 ())
    (treasury_deposit_per_coin . remainder) (divmod winning_byc_bid_amount treasury_coins_count)
    funding_coin_id (calculate-coin-id
      (f funding_coin_info)
      (curry_hashes CAT_MOD_HASH
        (sha256 ONE CAT_MOD_HASH)
        (sha256 ONE crt_tail_hash)
        RUN_TAIL_MOD_HASH
      )
      (r funding_coin_info)
    )
    (assert
      ; must have something to deposit
      (> treasury_deposit_per_coin 0)
      (> treasury_coins_count 0)
      (> winning_byc_bid_amount 0)
      (> winning_crt_bid_amount 0)
      ; bid must have timed out
      (> (- current_timestamp timestamp) bid_ttl)
      (li
        ; restart the auction
        (list
          CREATE_COIN
          (curry_hashes
            MOD_HASH
            (sha256 ONE MOD_HASH)
            (sha256tree STATUTES_STRUCT)
            (sha256 ONE LAUNCHER_ID)
            (sha256 ONE ())
            (sha256 ONE ())
          )
          0
        )
        (list REMARK
          PROTOCOL_PREFIX
          LAUNCHER_ID
          ()
          ()
        )
        (list ASSERT_MY_AMOUNT winning_byc_bid_amount)
        ; current_time minus one tx block time should already be in the past
        (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
        ; make sure that current_timestamp hasn't happen yet, allow it to be in mempool for 5 tx blocks
        (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp (* 5 MAX_TX_BLOCK_TIME)))
        ; construct funding coin id
        ; approve issuance of new CRT coins to target_puzzle_hash
        (list SEND_MESSAGE 0x3f
          (concat
            PROTOCOL_PREFIX
            (sha256tree
              (c STATUTES_STRUCT
                (c target_puzzle_hash winning_crt_bid_amount)
              )
            )
          )
          funding_coin_id
        )
        (list ASSERT_MY_PUZZLE_HASH
          (curry_hashes CAT_MOD_HASH
            (sha256 ONE CAT_MOD_HASH)
            (sha256 ONE BYC_TAIL_HASH)
            (curry_hashes
              MOD_HASH
              (sha256 ONE MOD_HASH)
              (sha256tree STATUTES_STRUCT)
              (sha256 ONE LAUNCHER_ID)
              (sha256tree AUCTION_PARAMS)
              (sha256tree LAST_BID)
            )
          )
        )
        (list ASSERT_MY_PARENT_ID
          (calculate-coin-id
            (f lineage_proof)
            (curry_hashes
              CAT_MOD_HASH
              (sha256 ONE CAT_MOD_HASH)
              (sha256 ONE BYC_TAIL_HASH)
              (tree_hash_of_apply MOD_HASH (f (r (r lineage_proof))))
            )
            ; parent bid amount
            (f (r lineage_proof))
          )
        )
        &rest
        ; evenly distribute the BYC amount locked in this coin (recharge auction) across all treasury coins
        (deposit-to-treasury
          TREASURY_MOD_HASH
          CAT_MOD_HASH
          BYC_TAIL_HASH
          STATUTES_STRUCT
          0 ; treasury minimum, pass 0 as unused
          treasury_deposit_per_coin
          remainder
          (f treasury_coins)
          treasury_coins
          input_conditions
          0 ; arg to sum up post-deposit treasury amounts
        )
      )
    )
  )
)
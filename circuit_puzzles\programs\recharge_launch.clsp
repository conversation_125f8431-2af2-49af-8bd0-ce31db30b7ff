(mod (
    (MOD_HASH STATUTES_STRUCT BYC_TAIL_HASH LAUNCHER_ID AUCTION_PARAMS LAST_BID)
    lineage_proof
    input_conditions
    (
      statutes_inner_puzzle_hash
      my_coin_id
    )
  )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include statutes_utils.clib)
  (include utils.clib)

  (assert
    ; enforce eve state
    (= LAUNCHER_ID 0)
    (= AUCTION_PARAMS ())
    (= LAST_BID ()) 
    (c
      (list
        CREATE_COIN
        (curry_hashes
          MOD_HASH
          (sha256 ONE MOD_HASH)
          (sha256tree STATUTES_STRUCT)
          (sha256 ONE my_coin_id) ; launcher ID
          (sha256 ONE ())
          (sha256 ONE ())
        )
        0
      )
      (c
        (list REMARK
          PROTOCOL_PREFIX
          my_coin_id
          () ; no params
          () ; no bids
        )
        (c
          (list ASSERT_MY_AMOUNT 0)
          (c
            (list ASSERT_MY_COIN_ID my_coin_id)
            (c
              ; get approval from statutes
              (list RECEIVE_MESSAGE 0x12
                (concat PROTOCOL_PREFIX CUSTOM_CONDITION_PREFIX (sha256tree '*'))
                (calculate-statutes-puzzle-hash STATUTES_STRUCT statutes_inner_puzzle_hash)
              )
              input_conditions
            )
          )
        )
      )
    )
  )
)

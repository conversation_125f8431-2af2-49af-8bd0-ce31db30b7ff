(

  (include prefixes.clib)

  (defconst STATUTE_PRICE_UPDATE_COUNTER -4)
  (defconst STATUTE_APPROVAL_MOD_HASHES_HASH -3)
  (defconst STATUTE_CUMULATIVE_INTEREST_DF -2)
  (defconst STATUTE_CUMULATIVE_STABILITY_FEE_DF -1)
  (defconst STATUTE_ORACLE_LAUNCHER_ID 0)
  (defconst STATUTE_STABILITY_FEE_DF 1)
  (defconst STATUTE_INTEREST_DF 2)
  (defconst STATUTE_CUSTOM_CONDITIONS 3)
  (defconst STATUTE_ORACLE_M_OF_N 4)
  (defconst STATUTE_ORACLE_PRICE_UPDATE_DELAY 5)
  (defconst STATUTE_ORACLE_PRICE_UPDATE_DELTA_BPS 6)
  (defconst STATUTE_PRICE_DELAY 7)
  (defconst STATUTE_VAULT_LIQUIDATION_RATIO_PCT 8)
  (defconst STATUTE_VAULT_MINIMUM_DEBT 9)
  (defconst STATUTE_VAULT_AUCTION_PRICE_TTL 10)
  (defconst STATUTE_VAULT_AUCTION_PRICE_DECREASE_BPS 11)
  (defconst STATUTE_VAULT_AUCTION_TTL 12)
  (defconst STATUTE_VAULT_AUCTION_STARTING_PRICE_FACTOR 13)
  (defconst STATUTE_VAULT_AUCTION_MINIMUM_BID 14)
  (defconst STATUTE_VAULT_INITIATOR_INCENTIVE_FLAT 15)
  (defconst STATUTE_VAULT_INITIATOR_INCENTIVE_BPS 16)
  (defconst STATUTE_VAULT_LIQUIDATION_PENALTY_BPS 17)
  (defconst STATUTE_TREASURY_MINIMUM 18)
  (defconst STATUTE_TREASURY_MAXIMUM 19)
  (defconst STATUTE_TREASURY_MINIMUM_DELTA 20)
  (defconst STATUTE_TREASURY_REBALANCE_DELTA_PCT 21)
  (defconst STATUTE_MINIMUM_BID_INCREASE_BPS 22)
  (defconst STATUTE_RECHARGE_AUCTION_TTL 23)
  (defconst STATUTE_RECHARGE_AUCTION_MINIMUM_CRT_PRICE 24)
  (defconst STATUTE_RECHARGE_AUCTION_BID_TTL 25)
  (defconst STATUTE_RECHARGE_AUCTION_MINIMUM_BID 26)
  (defconst STATUTE_SURPLUS_AUCTION_LOT 27)
  (defconst STATUTE_SURPLUS_AUCTION_BID_TTL 28)
  (defconst STATUTE_ANNOUNCER_CREDITS_PER_INTERVAL 29)
  (defconst STATUTE_ANNOUNCER_CREDITS_INTERVAL 30)
  (defconst STATUTE_ANNOUNCER_MINIMUM_DEPOSIT 31)
  (defconst STATUTE_ANNOUNCER_PRICE_TTL 32)
  (defconst STATUTE_ANNOUNCER_PENALTY_INTERVAL 33)
  (defconst STATUTE_ANNOUNCER_PENALTY_FACTOR_PER_INTERVAL_BPS 34)
  (defconst STATUTE_ANNOUNCER_DISAPPROVAL_MAXIMUM_PENALTY_BPS 35)
  (defconst STATUTE_ANNOUNCER_DISAPPROVAL_COOLDOWN_INTERVAL 36)
  (defconst STATUTE_GOVERNANCE_IMPLEMENTATION_INTERVAL 37)
  (defconst STATUTE_GOVERNANCE_COOLDOWN_INTERVAL 38)
  (defconst STATUTE_GOVERNANCE_BILL_PROPOSAL_FEE_MOJOS 39)


  (defconst STATUTES_MAX_IDX 39) ; max Statutes index

  (defconst STATUTE_PREFIX "s")
  (defconst STATUTE_FULL_PREFIX "S")
  (defconst PRICE_PREFIX "p")

  (defun-inline calculate-statutes-puzzle-hash (STATUTES_STRUCT inner_puzzle_hash)
    (curry_hashes
      (f STATUTES_STRUCT)
      ; we can also provide a hash of statutes struct to make it more efficient
      (if (l (r STATUTES_STRUCT)) (sha256tree STATUTES_STRUCT) (r STATUTES_STRUCT))
      inner_puzzle_hash
    )
  )

  (defun-inline assert-statute (statutes_puzzle_hash statute_index statute_value)
    (list ASSERT_PUZZLE_ANNOUNCEMENT
      (sha256
        statutes_puzzle_hash
        PROTOCOL_PREFIX
        (sha256tree (c STATUTE_PREFIX (c statute_index statute_value))) ; (if (l statute_value) (sha256tree statute_value) statute_value)
      )
    )
  )

  (defun-inline assert-full-statute (statutes_puzzle_hash statute_index statute_full_value)
    (list ASSERT_PUZZLE_ANNOUNCEMENT
      (sha256
        statutes_puzzle_hash
        PROTOCOL_PREFIX
        (sha256tree (c STATUTE_FULL_PREFIX (c statute_index statute_full_value))) ; (sha256tree statute_full_value)
      )
    )
  )

  (defun mutate-list (mutation_index new_value statutes index)
    (if statutes
      (c
        (if (= index mutation_index) new_value (f statutes))
        (mutate-list mutation_index new_value (r statutes) (+ index ONE))
      )
      (if (> mutation_index index)
        (x) ; mutation index out of bounds
        () ; no more statutes left and index is same as end of list
      )
    )
  )

  (defun-inline assert-price-info (statutes_puzzle_hash price_info)
    (list ASSERT_PUZZLE_ANNOUNCEMENT
      (sha256
        statutes_puzzle_hash
        PROTOCOL_PREFIX
        (sha256tree (c PRICE_PREFIX price_info))
      )
    )
  )
)

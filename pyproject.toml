[tool.poetry]
name = "circuit-puzzles"
packages = [{include = "circuit_puzzles"}]
version = "0.9.2"
description = "Puzzles used in stablecoin protocol Circuit on Chia blockchain"
authors = ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"]
exclude = [
    "circuit_puzzles/*.clsp",
    "circuit_puzzles/*.clib"
]

[tool.poetry.dependencies]
python = ">=3.11"
chialisp_loader = "^0.1.2"
chialisp_builder = "^0.1.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

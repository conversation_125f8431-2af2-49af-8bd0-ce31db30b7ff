# Circuit Puzzles
Circuit [puzzles](https://docs.chia.net/guides/crash-course/smart-coins) are what make the Circuit protocol tick. They are the building blocks of the protocol and used to create the stablecoin, Bytecash (BYC).

This is a Python package that includes all puzzles used by Circuit protocol.

## Project Status
A [public audit competition](https://cantina.xyz/competitions/7d650b99-8a40-49d1-9b65-2b060accfbb7) for Circuit is currently being held on Cantina.
* Anyone is eligible to participate
* Reward pool: USD 100,000
* Duration: 4 weeks, 19th of May until 16th of June 2025

The puzzles have undergone previous security reviews by Zellic and on Immunefi:
* [Zellic audit report](https://github.com/Zellic/publications/blob/master/Circuit%20DAO%20-%20Zellic%20Audit%20Report.pdf)
* [Immunefi audit competition](https://immunefi.com/audit-competition/iop-circuitdao/) 

An early version of these puzzles is currently in operation on [circuitdao.com](https://circuitdao.com) and undergoing testing on testnet 11.

## Contributing
We welcome contributions from the community.
If you encounter a bug or have a feature request, please open an issue.
For major changes, please open an issue first to discuss what you would like to change.

You can also reach us on [Discord](https://discord.gg/knHqg2nXca) if you have any questions or need further clarification.

## License 
All rights reserved to Voltage Technologies Ltd.

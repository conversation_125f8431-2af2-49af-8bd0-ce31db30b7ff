;;; this coin (payout coin) is controlled by a surplus auction coin, and is used to pay out funds to the winner of the surplus auction
; the payout coin is created with zero amount, and when it's first spent it's filled with the lot amount from treasury
; if the coin is spent again, its amount is transferred to the winner of the surplus auction
; every spend needs to be approved by the surplus auction coin
(mod (CAT_MOD_HASH
      MOD_HASH
      CRT_TAIL_HASH
      SURPLUS_AUCTION_MOD_HASH
      SURPLUS_AUCTION_LAUNCHER_ID
      surplus_auction_parent_id
      surplus_auction_curried_args_hash
      surplus_auction_amount
      puzzle_hash
      amount
    )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include prefixes.clib)
  (include utils.clib)

  (assign
    surplus_auction_coin_id (calculate-coin-id
      surplus_auction_parent_id
      (curry_hashes CAT_MOD_HASH
        (sha256 ONE CAT_MOD_HASH)
        (sha256 ONE CRT_TAIL_HASH)
        ; we're asserting that launcher coin must be using surplus puzzle so we can trust its announcements
        (tree_hash_of_apply SURPLUS_AUCTION_MOD_HASH surplus_auction_curried_args_hash)
      )
      surplus_auction_amount
    )
    (if (all (= surplus_auction_coin_id SURPLUS_AUCTION_LAUNCHER_ID) (= puzzle_hash ()))
      ; first spend of the coin
      (list  ; create / fill the coin from treasury
        ; surplus auction is being created and started, since we should have enough in treasury
        ; to fill this coin, let see. We also guarantee we locked them delegating control to surplus auction coin id
        (list SEND_MESSAGE 0x3f
          (concat PROTOCOL_PREFIX (sha256tree amount))
          surplus_auction_coin_id
        )
        ; recreate ourselves with the surplus BYC lot amount
        (list
          CREATE_COIN
          (curry_hashes MOD_HASH
            (sha256 ONE MOD_HASH)
            (sha256 ONE CRT_TAIL_HASH)
            (sha256 ONE SURPLUS_AUCTION_MOD_HASH)
            (sha256 ONE SURPLUS_AUCTION_LAUNCHER_ID)
          )
          amount
        )
        ; must be zero when locking the coin. also ensures this spend path can only be taken once
        ;   since Surplus Auction Lot Amount > 0 (or ow/ there's no possibility of loss to protocol)
        (list ASSERT_MY_AMOUNT 0)
      )
      ; we're transferring funds to surplus auction winner,
      ; we don't know if recent surplus coin contains our launcher id, but we're going to assert that with coin ann
      (list
        ; can only transfer full amount
        (list ASSERT_MY_AMOUNT amount)
        ; get confirmation from surplus auction that it indeed wants to transfer funds to the winner (puzzle_hash)
        ; and the winning bid amount to melt
        (list RECEIVE_MESSAGE 0x3f
          (concat
            PROTOCOL_PREFIX
            (sha256tree (c amount (c puzzle_hash SURPLUS_AUCTION_LAUNCHER_ID)))
          )
          surplus_auction_coin_id
        )
        ; transfer out
        (list CREATE_COIN puzzle_hash amount (list puzzle_hash))
      )
    )
  )
)

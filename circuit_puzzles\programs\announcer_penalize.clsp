; penalize announcer for not updating the price
(mod ((MOD_HASH STATUTES_STRUCT LAUNCHER_ID INNER_PUZZLE_HASH APPROVED DEPOSIT DELAY VALUE MIN_DEPOSIT
        CLAIM_COUNTER COOLDOWN_START PENALIZABLE_AT TIMESTAMP_EXPIRES
      )
      ; solution
      inner_puzzle_hash new_puzzle_hash deposit input_conditions
      (penalty_factor_per_interval statutes_inner_puzzle_hash penalty_interval_in_minutes min_deposit announcer_max_delay current_timestamp)
     )

  (include *standard-cl-23.1*)
  (include condition_codes.clib)
  (include curry.clib)
  (include utils.clib)
  (include statutes_utils.clib)
  (include announcer.clib)

  (assign
    penalized_deposit
      ; if not expired, we penalize per full deposit
      (/
        (*
          DEPOSIT
          penalty_factor_per_interval
        )
        PRECISION_BPS
      )
    statutes_puzzle_hash (calculate-statutes-puzzle-hash STATUTES_STRUCT statutes_inner_puzzle_hash)
    (assert
      ; must be past penalty timestamp to penalize
      (> current_timestamp PENALIZABLE_AT)
      (any
        ; penalize if expired
        (> current_timestamp TIMESTAMP_EXPIRES)
        ; penalize if deposit is less than announcer's min deposit
        (> MIN_DEPOSIT DEPOSIT)
        ; penalize if announcer's min deposit is less than Statues min deposit
        (> min_deposit MIN_DEPOSIT)
        ; penalize if announcer's delay is above expected delay
        (> DELAY announcer_max_delay)
      )
      APPROVED
      LAUNCHER_ID
      (> DEPOSIT penalized_deposit)
      (> penalized_deposit MINUS_ONE)
      (li
        (list RESERVE_FEE (- DEPOSIT penalized_deposit))
        (list ASSERT_SECONDS_ABSOLUTE (- current_timestamp MAX_TX_BLOCK_TIME))
        (list ASSERT_BEFORE_SECONDS_ABSOLUTE (+ current_timestamp MAX_TX_BLOCK_TIME))
        (assert-statute statutes_puzzle_hash STATUTE_ANNOUNCER_PENALTY_INTERVAL penalty_interval_in_minutes)
        (assert-statute statutes_puzzle_hash STATUTE_ANNOUNCER_PENALTY_FACTOR_PER_INTERVAL_BPS penalty_factor_per_interval)
        (assert-statute statutes_puzzle_hash STATUTE_ANNOUNCER_MINIMUM_DEPOSIT min_deposit)
        (assert-statute statutes_puzzle_hash STATUTE_ANNOUNCER_PRICE_TTL announcer_max_delay)
        (list ASSERT_SECONDS_ABSOLUTE TIMESTAMP_EXPIRES)
        &rest
        (recreate-myself-condition MOD_HASH
          STATUTES_STRUCT
          LAUNCHER_ID
          INNER_PUZZLE_HASH
          APPROVED
          penalized_deposit
          DELAY
          VALUE
          MIN_DEPOSIT
          CLAIM_COUNTER
          COOLDOWN_START
          (+ current_timestamp (* 60 penalty_interval_in_minutes))
          TIMESTAMP_EXPIRES
          input_conditions
        )
      )
    )
  )
)
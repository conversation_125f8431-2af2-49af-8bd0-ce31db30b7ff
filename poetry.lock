# This file is automatically @generated by Poetry 2.1.2 and should not be changed by hand.

[[package]]
name = "chialisp-builder"
version = "0.1.2"
description = "Allow on-demand builds of chialisp with recursive dependency checking."
optional = false
python-versions = "*"
groups = ["main"]
files = [
    {file = "chialisp_builder-0.1.2.tar.gz", hash = "sha256:658a8baf70ee8cdc0466c429173312be1ff05aace4852fc078c9bf1cfdd60d62"},
]

[package.dependencies]
clvm_tools_rs = "*"
runtime_builder = ">=0.1.3"

[[package]]
name = "chialisp-loader"
version = "0.1.2"
description = "Provides `load_puzzle` which dynamic rebuilds if `chialisp_builder` is available."
optional = false
python-versions = "*"
groups = ["main"]
files = [
    {file = "chialisp_loader-0.1.2.tar.gz", hash = "sha256:085080fc7d4a3fab90fe93db3c599391b869047846608da1ae92b54b4100e8a6"},
]

[package.dependencies]
clvm_rs = "*"
importlib_resources = "*"

[[package]]
name = "clvm-rs"
version = "0.12.1"
description = "Implementation of `clvm` for Chia Network's cryptocurrency"
optional = false
python-versions = "*"
groups = ["main"]
files = [
    {file = "clvm_rs-0.12.1-cp38-abi3-macosx_13_0_arm64.whl", hash = "sha256:9fab668022be2618c298bd2b274bdace4d51ff845ee2861b5ba740caefa7b570"},
    {file = "clvm_rs-0.12.1-cp38-abi3-macosx_13_0_x86_64.whl", hash = "sha256:9761594c5f01ede0d2724c715d9f9e87ce44e7d03e09201610afe0ce3070b2ac"},
    {file = "clvm_rs-0.12.1-cp38-abi3-manylinux_2_28_aarch64.whl", hash = "sha256:7bc8c7373ca8ca2c6d6276f4970629cc3e266199d16cf4f44dc8e057a774c6d5"},
    {file = "clvm_rs-0.12.1-cp38-abi3-manylinux_2_28_x86_64.whl", hash = "sha256:5881d445a389247eaaeb6effc9fe095b372db5b5b28536e92a105694592a8e2c"},
    {file = "clvm_rs-0.12.1-cp38-abi3-win_amd64.whl", hash = "sha256:4f4bf668dce231831baf14a73e08f5aa834d086815d77298639efa0901e6a45e"},
    {file = "clvm_rs-0.12.1.tar.gz", hash = "sha256:4475f1fe16c40778770bfa7378012f3ab5ac351c71374bdd836fb89dded7fbc5"},
]

[[package]]
name = "clvm-tools-rs"
version = "0.1.48"
description = "tools for working with chialisp language; compiler, repl, python and wasm bindings"
optional = false
python-versions = "*"
groups = ["main"]
files = [
    {file = "clvm_tools_rs-0.1.48-cp38-abi3-macosx_13_0_arm64.whl", hash = "sha256:8360760d67857a7864c20ce0b9d33f38e2df9f177d62a5603c44361daeb534d2"},
    {file = "clvm_tools_rs-0.1.48-cp38-abi3-macosx_13_0_x86_64.whl", hash = "sha256:7fb1538c3427c12f2bc636114a8776399e0a525dce47544181d3b979735db32c"},
    {file = "clvm_tools_rs-0.1.48-cp38-abi3-manylinux_2_28_aarch64.whl", hash = "sha256:2dd11e50d3073d45f6ed5ec1900907773d512b4a406f24bd05169ee49fe7eda5"},
    {file = "clvm_tools_rs-0.1.48-cp38-abi3-manylinux_2_28_x86_64.whl", hash = "sha256:55b4902636b9ea8f284ad00d80aa912910383e83c499c0f88588ff9f3e76bf8d"},
    {file = "clvm_tools_rs-0.1.48-cp38-abi3-musllinux_1_1_x86_64.whl", hash = "sha256:23e858080db6bd3e3cda65b212bef00d072e181247970940fe77d1e9ec528e69"},
    {file = "clvm_tools_rs-0.1.48-cp38-abi3-win_amd64.whl", hash = "sha256:5e7dd96b08ffa8a768fd4f78dc0d8fe2b6e4f2958604cc5955ee9813592e6385"},
    {file = "clvm_tools_rs-0.1.48.tar.gz", hash = "sha256:03f60301c22d0cb190cdb29b540d72ef1c73f1d4a0c9bd93505b8d74c93fc026"},
]

[[package]]
name = "importlib-resources"
version = "6.5.2"
description = "Read resources from Python packages"
optional = false
python-versions = ">=3.9"
groups = ["main"]
files = [
    {file = "importlib_resources-6.5.2-py3-none-any.whl", hash = "sha256:789cfdc3ed28c78b67a06acb8126751ced69a3d5f79c095a98298cd8a760ccec"},
    {file = "importlib_resources-6.5.2.tar.gz", hash = "sha256:185f87adef5bcc288449d98fb4fba07cea78bc036455dd44c5fc4a2fe78fed2c"},
]

[package.extras]
check = ["pytest-checkdocs (>=2.4)", "pytest-ruff (>=0.2.1) ; sys_platform != \"cygwin\""]
cover = ["pytest-cov"]
doc = ["furo", "jaraco.packaging (>=9.3)", "jaraco.tidelift (>=1.4)", "rst.linker (>=1.9)", "sphinx (>=3.5)", "sphinx-lint"]
enabler = ["pytest-enabler (>=2.2)"]
test = ["jaraco.test (>=5.4)", "pytest (>=6,!=8.1.*)", "zipp (>=3.17)"]
type = ["pytest-mypy"]

[[package]]
name = "runtime-builder"
version = "0.1.5"
description = "Allow automatic builds in edit mode"
optional = false
python-versions = "*"
groups = ["main"]
files = [
    {file = "runtime_builder-0.1.5.tar.gz", hash = "sha256:c94256fc7112d4a392d7eadd701c490328d82ef308ee7807bd203b9cd58b3a1d"},
]

[package.dependencies]
importlib_resources = ">=6.1.1"

[metadata]
lock-version = "2.1"
python-versions = ">=3.11"
content-hash = "1b21c23fbe5d981833714ca3c4e9a343d298ee9e5082e4827e8c2a2b8e1b349f"

(

  ;; library for safely manipulating merkle trees
  ;;
  ;; this library assumes that the merkle tree being manipulated is dense,
  ;; ie the number of leaves is a power of 2. leaves that are not being used
  ;; are set to a dummy coin ID, ZERO_LEAF, as placeholder.

  (defconstant ZERO_LEAF 0x0000000000000000000000000000000000000000000000000000000000000000)


  (defun list-length_ (lst length)
    (if lst
      (list-length_ (r lst) (+ length 1))
      length
    )
  )

  ;; return number of elements in list
  (defun list-length (lst)
    (list-length_ lst 0)
  )

  ;; lower median index of a list
  ;; first list index is 0
  ;; if list has an even number of elements, round median index down
  ;; returns -1 if () is passed
  (defun lower-median-index-list (lst)
    (/ (- (list-length lst) 1) 2)
  )

  (defun reverse-list_ (lst reversed_list)
    (if lst
      (reverse-list_ (r lst) (c (f lst) reversed_list))
      reversed_list
    )
  )

  ;; reverse list
  (defun reverse-list (lst)
    (reverse-list_ lst ())
  )

  (defun split-list_ (lst index lower_part)
    (if lst
      (if (> index -1)
        (split-list_ (r lst) (- index 1) (c (f lst) lower_part))
        (c (reverse-list lower_part) (c lst ()))
      )
      (c (reverse-list lower_part) (c () ()))
    )
  )

  ;; split list at index
  ;; returns a list of two lists, either of which may be empty (i.e. nil)
  ;; if the list passed in is empty, a list of two nil values gets returned
  ;; index should be in (-1 .. length of list - 1)
  ;; if index <= -1, then lower part = () and upper part is the whole list
  ;; if index >= length of list - 1, then lower part is the whole list and upper part = ()
  (defun split-list (lst index)
    (split-list_ lst index ())
  )


  ;; turn a list into a binary tree in canonical fashion by
  ;; recursively splitting list at lower median index
  (defun list-to-binary-tree (lst)
    (if (> (list-length lst) 2) ; only keep splitting if list length is > 2
      (c
        (list-to-binary-tree (f (split-list lst (lower-median-index lst))))
	(list-to-binary-tree (f (r (split-list lst (lower-median-index lst)))))
      )
      (if (> (list-length lst) 1)
        (c (f lst) (f (r lst))) ; convert lists of length 2 into cons box
	(if (> (list-length lst) 0)
	  (f lst) ; convert list of length 1 into individual element
	  () ; empty list remains empty list
	)
      )
    )
  )

  (defun identical-elements-list_ (multiplicity element lst)
    (if (> multiplicity 0)
      (identical-elements-list_ (- multiplicity 1) element (c element lst))
      lst
    )
  )

  ;; returns a list which contains the element exactly multiplicity times
  ;; for example, n = 3 and element = "0xdeadbeef" results in
  ;; (list 0xdeadbeef 0xdeadbeef 0xdeadbeef)
  (defun identical-elements-list (multiplicity element)
    (identical-elements-list_ multiplicity element ())
  )

  ;; descend a merkle tree from leaf to root according to the proof provided
  ;; bitpath is traversed from right to left (least to most significant bit)
  ;; hashes_path is traversed from left to right
  ;; returns the root of the merkle tree
  (defun simplify_merkle_proof_after_leaf (leaf_hash (bitpath . hashes_path))
    (if hashes_path
        (simplify_merkle_proof_after_leaf
            (if (logand 1 bitpath) ; true if least significant bit is 1, false o/w
                (sha256 0x02 (f hashes_path) leaf_hash)
                (sha256 0x02 leaf_hash (f hashes_path))
            )
            (c (lsh bitpath -1) (r hashes_path)) ; bitwise right shift. drop least significant bit from bitpath
        )
        leaf_hash
     )
  )

  ;; return merkle root for given leaf and proof
  (defun-inline simplify_merkle_proof (leaf proof)
    (simplify_merkle_proof_after_leaf (sha256 0x01 leaf) proof)
  )

  ;; insert a leaf in the merkle tree
  ;; requires a proof for a zero leaf in the tree to be passed in as an argument
  ;; proof = (bitpath . hashes_path)
  ;; returns the merkle root after the new leaf has been inserted
  (defun insert-leaf (merkle_root proof new_leaf)
    (if
      (all
        (not (= new_leaf ZERO_LEAF)) ; can't insert a zero leaf. use delete-leaf function instead
        (= merkle_root (simplify_merkle_proof ZERO_LEAF proof)) ; proof that we are inserting in an empty slot
      )
      (simplify_merkle_proof new_leaf proof) ; insert the new leaf
      (x)
    )
  )

  ;; delete a leaf of maximum depth from a merkle tree
  ;; requires a proof for the leaf to be deleted to be passed in as an argument
  ;; checks that only leaves at the max depth of the tree can be deleted (root has depth = 1)
  ;; proof = (bitpath . hashes_path)
  ;; returns the merkle root after leaf has been replaced by the zero leaf
  (defun delete-leaf ((merkle_root . tree_depth) leaf proof)
    (if
      (all
        (not (= leaf ZERO_LEAF)) ; can't delete zero leaf
	      (= (list-length (r proof)) (- tree_depth 1)) ; check length of hashes path
        (= merkle_root (simplify_merkle_proof leaf proof)) ; proof that we are deleting the correct leaf
      )
      (simplify_merkle_proof ZERO_LEAF proof) ; insert the zero leaf
      (x)
    )
  )

  ; replacement would require a check on the length of the proof, o/w nodes rather than leaves could be replaced
  ;
  ; ;; replace a leaf in the merkle tree
  ; ;; requires a proof for the leaf to be replaced to be passed in as an argument
  ; ;; proof = (bitpath . hashes_path)
  ; ;; returns the merkle root after leaf has been replaced with the new leaf
  ; (defun replace-leaf (merkle_root leaf proof new_leaf)
  ;   (assert
  ;     (all
  ;       (not (= leaf ZERO_LEAF)) ; can't replace a zero leaf. use insert-leaf function
  ;       (= merkle_root (simplify_merkle_proof leaf proof)) ; proof that we are replacing the correct leaf
  ;     )
  ;     (simplify_merkle_proof new_leaf proof) ; insert the zero leaf
  ;   )
  ; )

)
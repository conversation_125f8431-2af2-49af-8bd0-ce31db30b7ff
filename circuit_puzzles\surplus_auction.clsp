(mod (OPERATIONS CAT_MOD_HASH BYC_TAIL_MOD_HASH CRT_TAIL_MOD_HASH
      MOD_HASH
      STATUTES_STRUCT ; to assert statutes
      LAUNCHER_ID ; to assert uniqueness of launcher_id
      BID_TTL
      MIN_BID_INCREASE_BPS
      BYC_LOT_AMOUNT ; amount of BYC in a lot to win
      LAST_BID ; holds bid data -> (target_puzzle_hash . timestamp). amount of CRT bid is surplus auction coin amount
      lineage_proof ; -> (parent_id launcher_id amount last_bid)
      operation input_conditions . op_args)

  (include *standard-cl-23.1*)
  (include prefixes.clib)
  (include utils.clib)
  (include condition_codes.clib)
  (include condition_filtering.clib)
  (include curry.clib)

  (defun-inline is-valid-operation (operation_hash OPERATIONS)
    (if
      (any
        (= (f OPERATIONS) operation_hash)
        (= (f (r OPERATIONS)) operation_hash)
        (= (f (r (r OPERATIONS))) operation_hash)
      )
      1
      (x)
    )
  )

  (assign-inline
    byc_tail_hash (curry_hashes BYC_TAIL_MOD_HASH (sha256tree STATUTES_STRUCT))
    crt_tail_hash (curry_hashes CRT_TAIL_MOD_HASH (sha256tree STATUTES_STRUCT))
    (assert
      (is-valid-operation (sha256tree operation) OPERATIONS)
      (fail-on-protocol-condition-or-create-coin input_conditions)
      (a
        operation
        (list
          (list MOD_HASH CAT_MOD_HASH STATUTES_STRUCT byc_tail_hash crt_tail_hash LAUNCHER_ID BID_TTL MIN_BID_INCREASE_BPS BYC_LOT_AMOUNT LAST_BID)
          lineage_proof input_conditions op_args
        )
      )
    )
  )
)
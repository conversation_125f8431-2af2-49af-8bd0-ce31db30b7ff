(mod
  (
    (@ VAULT_STATE
      (
       COLLATERAL PRINCIPAL AUCTION_STATE INNER_PUZZLE
       STATUTES_STRUCT DISCOUNTED_PRINCIPAL statutes_puzzle_hash
      )
    )
    (@ args
      (
         target_puzzle_hash
      )
    )
  )


  (list
    (list COLLATERAL PRINCIPAL AUCTION_STATE target_puzzle_hash DISCOUNTED_PRINCIPAL)
    ; nothing else to assert here, just create a coin with new inner puzzle (target_puzzle_hash)
    (list )
  )
)
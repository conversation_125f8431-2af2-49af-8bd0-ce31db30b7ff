(
  (defconst ONE 1)
  (defconst MINUS_ONE -1)
  (defconst TWO 2)
  (defconst MOJOS 1000000000000)
  (defconst PRECISION_PCT 100)
  (defconst PRECISION_BPS 10000)
  (defconst PRICE_PRECISION 100)
  (defconst MAX_TX_BLOCK_TIME 120)
  (defconst PRECISION 10000000000)
  (defconst ONE_MINUTE 60)
  (defconst b32 32)
  (defconst TRUE 1)
  (defconst FALSE 0)

  (defun-inline size_b32 (var)
    (= (strlen var) b32)
  )

  (defun-inline size_uint32 (var)
    ; 5 bytes for uint32
    (all (> var MINUS_ONE) (> 6 (strlen var)))
  )

  (defun-inline size_uint64 (var)
    ; 9 bytes for uint64
    (all (> var MINUS_ONE) (> 10 (strlen var)))
  )

  (defun-inline size_b1 (var)
    ; zero or one byte
    (all (> (strlen var) MINUS_ONE) (> TWO (strlen var)))
  )

  (defun and_ (CLAUSES)
    (if (r CLAUSES)
      (qq (if (unquote (f CLAUSES)) (unquote (and_ (r CLAUSES))) ()))
      (f CLAUSES)
      )
    )

  (defmac and CLAUSES (if CLAUSES (and_ CLAUSES) ONE))

  (defun or_ (CLAUSES)
    (if (r CLAUSES) ;; There are more.
      (qq (if (unquote (f CLAUSES)) (unquote (f CLAUSES)) (unquote (or_ (r CLAUSES)))))
      (f CLAUSES)
      )
    )

  (defmac or CLAUSES (if CLAUSES (or_ CLAUSES) ()))

  (defun calculate-cumulative-discount-factor (past_cumulative_discount_factor current_discount_factor current_timestamp previous_timestamp)
    (if (> previous_timestamp current_timestamp)
      past_cumulative_discount_factor
      (calculate-cumulative-discount-factor
        (/ (* past_cumulative_discount_factor current_discount_factor) PRECISION)
        current_discount_factor
        current_timestamp
        ; per minute calculation
        (+ previous_timestamp ONE_MINUTE)
      )
    )
  )

  (defun validate-conditions-for-invalid-msgs ( (@ conditions ((condition_code . condition_rest) . rest_of_conditions)) coin_id)
    (if conditions
      (if
        (or
          (= condition_code SEND_MESSAGE)
          (= condition_code RECEIVE_MESSAGE)
        )
        (if (= (f (r (r condition_rest))) coin_id)
          (x)
          (validate-conditions-for-invalid-msgs rest_of_conditions coin_id)
        )
        (validate-conditions-for-invalid-msgs rest_of_conditions coin_id)
      )
      ONE
    )
  )

  (defun-inline calculate-coin-id (parent puzzle_hash amount)
    (coinid parent puzzle_hash amount)
  )

  (defun-inline is-approval-mod (approval_mod_hashes approval_mod)
    (any
      (= (f approval_mod_hashes) approval_mod)
      (= (f (r approval_mod_hashes)) approval_mod)
      (= (f (r (r approval_mod_hashes))) approval_mod)
      (= (f (r (r (r approval_mod_hashes)))) approval_mod)
      (= (f (r (r (r (r approval_mod_hashes))))) approval_mod)
    )
  )


  (defun contains (lst item)
    (if lst
      (if (= (f lst) item)
        ONE
        (contains (r lst) item)
      )
      ; not found
      ()
    )
  )

  (defun assert_debug_ (items)
    (if (r items)
      (qq (if (unquote (f items)) (unquote (assert_debug_ (r items))) (x (unquote (c 1 (f items))))))
      (f items)
    )
  )

  ; this also shows which part of assert failed
  (defmac assert_debug items (assert_debug_ items))

  (defun assert_ (items)
    (if (r items)
      (qq (if (unquote (f items)) (unquote (assert_ (r items))) (x)))
      (f items)
      )
    )

  (defmac assert items (assert_ items))

  (defun sha256tree (TREE)
    (if (l TREE)
      (sha256 TWO (sha256tree (f TREE)) (sha256tree (r TREE)))
      (sha256 ONE TREE)
    )
  )

  (defun merge-lists (list_a list_b)
    (if list_a
      (c
        (f list_a)
        (merge-lists
          (r list_a)
          list_b)
        )
      list_b
    )
  )

  (defun filter (pred (@ lst (first . rest)))
    (if lst
      (assign item (a pred (list first))
        (if item
          (c item (filter pred rest))
          (filter pred rest)
        )
      )
      ()
    )
  )

  (defun print (to-show result) (if (all "$print$" to-show result) result result))

  (defun-inline li x x)
)
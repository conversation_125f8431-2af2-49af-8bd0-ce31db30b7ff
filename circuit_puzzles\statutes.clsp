(mod (
      APPROVAL_MOD_HASHES_HASH ; hash of list of approval mod hashes
      OPERATIONS
      MOD_HASH
      ; indicates whether previous spend was an announce (1) or not (()). Set to 2 in eve state
      PREV_ANNOUNCE 
      STATUTES ; -> ((value proposal_threshold veto_period implementation_delay maximum_delta))
      PRICE_INFO ; Statute price info. -> (price . last_updated)
      ; cumulative stability fee discount factor as of last Statutes price info update
      PAST_CUMULATIVE_STABILITY_FEE_DF
      ; cumulative interest rate discount factor as of last Statutes price info update
      PAST_CUMULATIVE_INTEREST_DF
      ; counter of all price updates
      PRICE_UPDATE_COUNTER

      ; solution
      governance_curried_args_hash ; as required by tree_hash_of_apply
      mutation
     )

  (include *standard-cl-23.1*)
  (include curry.clib)
  (include condition_codes.clib)
  (include statutes_utils.clib)
  (include utils.clib)

  (defun announce-statutes (statutes start_index rest)
    (if statutes
      (c
        (list CREATE_PUZZLE_ANNOUNCEMENT
          (concat PROTOCOL_PREFIX
            (sha256tree (c STATUTE_PREFIX (c start_index (f (f statutes)))))
          )
        )
        ; add a full statutes when announcing statutes
        (c
          (list CREATE_PUZZLE_ANNOUNCEMENT
            (concat PROTOCOL_PREFIX
              (sha256tree (c STATUTE_FULL_PREFIX (c start_index (f statutes))))
            )
          )
          (announce-statutes (r statutes) (+ start_index ONE) rest)
        )
      )
      rest
    )
  )

  (defun-inline create-coin-condition (MOD_HASH
                                        prev_announce statutes price_info
                                        cumulative_stability_fee_df cumulative_interest_df price_update_counter)
    (list
      CREATE_COIN
      (curry_hashes MOD_HASH
        (sha256 ONE MOD_HASH)
        (sha256 ONE prev_announce)
        (sha256tree statutes)
        (sha256tree price_info)
        (sha256 ONE cumulative_stability_fee_df)
        (sha256 ONE cumulative_interest_df)
        (sha256 ONE price_update_counter)
      )
      ; amount, 1 mojo for singletons or any odd number
      ONE
    )
  )


  (assign
    (operation mutation_index mutation_value) (if mutation mutation (() () ()))
    (operation_mod . operation_mod_hash) (if operation (c operation (sha256tree operation)) (c () ()))
    (prev_announce statutes price_info cumulative_stability_fee_df cumulative_interest_df price_update_counter operation_conditions)
      (if operation_mod_hash
        ; mutate statutes or update price info
        (assert
          (= PREV_ANNOUNCE ONE) ; previous spend must have been an announce
          (any (= operation_mod_hash (f OPERATIONS)) (= operation_mod_hash (r OPERATIONS)))
          (a
            operation_mod
            (list
              MOD_HASH STATUTES PRICE_INFO PAST_CUMULATIVE_STABILITY_FEE_DF PAST_CUMULATIVE_INTEREST_DF
              PRICE_UPDATE_COUNTER
              mutation_index mutation_value
              governance_curried_args_hash
            )
          )
        )
        ; announce
        (assert
          (= governance_curried_args_hash ()) ; ensure unique solution when announcing to facilitate identical spend aggregation
          (list
            ; set PREV_ANNOUNCE to 1
            ONE
            ; keep remaining mutable state unchanged
            STATUTES PRICE_INFO PAST_CUMULATIVE_STABILITY_FEE_DF PAST_CUMULATIVE_INTEREST_DF PRICE_UPDATE_COUNTER
            ; operation conditions
            (if (> PREV_ANNOUNCE ONE)
              ; eve spend
              ()
              ; non-eve spend
              (list
                (list ASSERT_HEIGHT_RELATIVE 0) ; no prior ephemeral spend allowed
              )
            )
          )
        )
      )
    (li
      (create-coin-condition
        MOD_HASH
        prev_announce
        statutes
        price_info
        cumulative_stability_fee_df
        cumulative_interest_df
        price_update_counter
      )
      (list REMARK PROTOCOL_PREFIX prev_announce statutes price_info cumulative_stability_fee_df cumulative_interest_df price_update_counter)
      (list CREATE_PUZZLE_ANNOUNCEMENT
        (concat PROTOCOL_PREFIX (sha256tree (c STATUTE_PREFIX (c -4 price_update_counter))))
      )
      (list CREATE_PUZZLE_ANNOUNCEMENT
        (concat PROTOCOL_PREFIX (sha256tree (c STATUTE_PREFIX (c -3 APPROVAL_MOD_HASHES_HASH))))
      )
      (list CREATE_PUZZLE_ANNOUNCEMENT
        (concat PROTOCOL_PREFIX (sha256tree (c STATUTE_PREFIX (c -2 cumulative_interest_df))))
      )
      (list CREATE_PUZZLE_ANNOUNCEMENT
        (concat PROTOCOL_PREFIX (sha256tree (c STATUTE_PREFIX (c MINUS_ONE cumulative_stability_fee_df))))
      )
      (list CREATE_PUZZLE_ANNOUNCEMENT
        (concat PROTOCOL_PREFIX (sha256tree (c PRICE_PREFIX price_info)))
      )
      &rest
      (announce-statutes STATUTES 0 operation_conditions) ; always announce existing statutes, even when mutating
    )
  )
)
